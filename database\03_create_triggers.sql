-- =====================================================
-- Malombo Admin Panel - Database Triggers
-- =====================================================
-- This file creates database triggers for automatic
-- timestamp updates and other automated operations.
-- 
-- Safe to run multiple times (idempotent)
-- =====================================================

-- =====================================================
-- FUNCTION: Update timestamp on row modification
-- =====================================================

-- Drop function if it exists (for idempotency)
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    -- Set the updated_at column to current timestamp
    NEW.updated_at = NOW();
    
    -- Log the update (optional, for debugging)
    -- RAISE NOTICE 'Updated timestamp for table: %, id: %', TG_TABLE_NAME, NEW.id;
    
    RETURN NEW;
END;
$$;

-- Add comment to function
COMMENT ON FUNCTION public.update_updated_at_column() IS 'Automatically updates updated_at timestamp when a row is modified';

-- =====================================================
-- FUNCTION: Handle new user registration
-- =====================================================

-- Drop function if it exists (for idempotency)
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

-- Create function to automatically create profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    -- Insert a new profile record for the newly created auth user
    INSERT INTO public.profiles (id, email, role, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        'staff', -- Default role is staff
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', '')
    );
    
    -- Log the profile creation
    RAISE NOTICE 'Created profile for new user: %', NEW.email;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation
        RAISE WARNING 'Failed to create profile for user %: %', NEW.email, SQLERRM;
        RETURN NEW;
END;
$$;

-- Add comment to function
COMMENT ON FUNCTION public.handle_new_user() IS 'Automatically creates a profile record when a new user signs up';

-- =====================================================
-- FUNCTION: Update last login timestamp
-- =====================================================

-- Drop function if it exists (for idempotency)
DROP FUNCTION IF EXISTS public.update_last_login() CASCADE;

-- Create function to update last login timestamp
CREATE OR REPLACE FUNCTION public.update_last_login()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update last_login_at when user signs in
    UPDATE public.profiles 
    SET last_login_at = NOW()
    WHERE id = NEW.user_id;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the login
        RAISE WARNING 'Failed to update last login for user %: %', NEW.user_id, SQLERRM;
        RETURN NEW;
END;
$$;

-- Add comment to function
COMMENT ON FUNCTION public.update_last_login() IS 'Updates last_login_at timestamp when user signs in';

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Drop existing triggers if they exist (for idempotency)
DROP TRIGGER IF EXISTS trigger_update_profiles_updated_at ON public.profiles;
DROP TRIGGER IF EXISTS trigger_handle_new_user ON auth.users;

-- Trigger 1: Update updated_at timestamp on profiles table
CREATE TRIGGER trigger_update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Trigger 2: Create profile when new user signs up
CREATE TRIGGER trigger_handle_new_user
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Note: Last login trigger would be on auth.sessions or similar
-- This depends on your specific Supabase setup and requirements

-- Add comments to triggers
COMMENT ON TRIGGER trigger_update_profiles_updated_at ON public.profiles IS 'Automatically updates updated_at timestamp when profile is modified';
COMMENT ON TRIGGER trigger_handle_new_user ON auth.users IS 'Automatically creates profile record when new user signs up';

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.update_updated_at_column() TO authenticated;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_last_login() TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Database triggers created successfully:';
    RAISE NOTICE '- update_updated_at_column(): Updates timestamps automatically';
    RAISE NOTICE '- handle_new_user(): Creates profiles for new users';
    RAISE NOTICE '- update_last_login(): Tracks user login times';
END $$;
