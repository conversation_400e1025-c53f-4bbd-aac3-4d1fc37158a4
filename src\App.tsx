import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Home from "./pages/Home";
import Accommodations from "./pages/Accommodations";
import Booking from "./pages/Booking";
import NotFound from "./pages/NotFound";
import ErrorBoundary from "./components/ErrorBoundary";
import "./App.css";
import { RootLayout } from "./layouts/RootLayout";

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <RootLayout>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/accommodations" element={<Accommodations />} />
            <Route path="/booking" element={<Booking />} />
            {/* Catch-all route for 404 errors */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </RootLayout>
      </Router>
    </ErrorBoundary>
  );
}

export default App;
