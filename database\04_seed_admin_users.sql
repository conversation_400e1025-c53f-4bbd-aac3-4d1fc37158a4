-- =====================================================
-- Malombo Admin Panel - Seed Admin Users
-- =====================================================
-- This file creates initial admin and staff users for testing.
-- 
-- ⚠️  IMPORTANT: These are TEST ACCOUNTS with default passwords!
-- ⚠️  Change passwords and remove test accounts before production!
-- 
-- Safe to run multiple times (idempotent)
-- =====================================================

-- =====================================================
-- SEED DATA: Test User Profiles
-- =====================================================

-- Note: You need to create these users in Supabase Auth first,
-- then run this script to add their profile data.
-- 
-- Test Users to Create in Supabase Auth:
-- 1. <EMAIL> (password: Admin123!)
-- 2. <EMAIL> (password: Staff123!)
-- 3. <EMAIL> (password: Manager123!)

-- Clear existing test data (for idempotency)
DELETE FROM public.profiles 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
);

-- =====================================================
-- INSERT TEST PROFILES
-- =====================================================
-- Note: The UUIDs below are examples. In practice, you'll need to:
-- 1. Create users in Supabase Auth first
-- 2. Get their actual UUIDs from auth.users table
-- 3. Update this script with the real UUIDs

-- Example admin user profile
-- Replace 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee' with actual UUID from auth.users
INSERT INTO public.profiles (
    id, 
    email, 
    role, 
    first_name, 
    last_name, 
    phone, 
    is_active,
    created_at,
    updated_at
) VALUES (
    'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee'::uuid,
    '<EMAIL>',
    'admin',
    'Admin',
    'User',
    '+255 123 456 789',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Example staff user profile
-- Replace 'bbbbbbbb-cccc-dddd-eeee-ffffffffffff' with actual UUID from auth.users
INSERT INTO public.profiles (
    id, 
    email, 
    role, 
    first_name, 
    last_name, 
    phone, 
    is_active,
    created_at,
    updated_at
) VALUES (
    'bbbbbbbb-cccc-dddd-eeee-ffffffffffff'::uuid,
    '<EMAIL>',
    'staff',
    'Staff',
    'Member',
    '+255 123 456 790',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Example manager user profile (admin role)
-- Replace 'cccccccc-dddd-eeee-ffff-aaaaaaaaaaaa' with actual UUID from auth.users
INSERT INTO public.profiles (
    id, 
    email, 
    role, 
    first_name, 
    last_name, 
    phone, 
    is_active,
    created_at,
    updated_at
) VALUES (
    'cccccccc-dddd-eeee-ffff-aaaaaaaaaaaa'::uuid,
    '<EMAIL>',
    'admin',
    'Camp',
    'Manager',
    '+255 123 456 791',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- =====================================================
-- HELPER QUERY: Get User UUIDs from Auth
-- =====================================================
-- Run this query to get the actual UUIDs after creating users in Supabase Auth:

/*
SELECT 
    id,
    email,
    created_at,
    email_confirmed_at
FROM auth.users 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY email;
*/

-- =====================================================
-- VERIFICATION QUERY
-- =====================================================
-- Run this query to verify the profiles were created correctly:

/*
SELECT 
    p.id,
    p.email,
    p.role,
    p.first_name,
    p.last_name,
    p.is_active,
    p.created_at,
    u.email_confirmed_at
FROM public.profiles p
LEFT JOIN auth.users u ON p.id = u.id
WHERE p.email LIKE '%@malombo.com'
ORDER BY p.role DESC, p.email;
*/

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Test user profiles seeded successfully!';
    RAISE NOTICE '';
    RAISE NOTICE 'IMPORTANT NEXT STEPS:';
    RAISE NOTICE '1. Create these users in Supabase Auth Dashboard:';
    RAISE NOTICE '   - <EMAIL> (password: Admin123!)';
    RAISE NOTICE '   - <EMAIL> (password: Staff123!)';
    RAISE NOTICE '   - <EMAIL> (password: Manager123!)';
    RAISE NOTICE '';
    RAISE NOTICE '2. Get their UUIDs from auth.users table';
    RAISE NOTICE '3. Update this script with real UUIDs';
    RAISE NOTICE '4. Re-run this script';
    RAISE NOTICE '';
    RAISE NOTICE 'WARNING: Change passwords before production!';
END $$;
