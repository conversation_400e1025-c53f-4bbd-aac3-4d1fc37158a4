import { supabase } from './supabase'
import type { User } from '@supabase/supabase-js'

export interface UserProfile {
  id: string
  email: string
  role: 'admin' | 'staff'
}

export interface AuthState {
  user: User | null
  profile: UserProfile | null
  loading: boolean
}

// Sign in with email and password
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  
  if (error) throw error
  return data
}

// Sign out
export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

// Get current user
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

// Get user profile with role
export const getUserProfile = async (userId: string): Promise<UserProfile | null> => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
  
  return data
}

// Check if user has admin role
export const isAdmin = (profile: UserProfile | null): boolean => {
  return profile?.role === 'admin'
}

// Check if user has staff role or higher
export const isStaffOrAdmin = (profile: UserProfile | null): boolean => {
  return profile?.role === 'staff' || profile?.role === 'admin'
}
